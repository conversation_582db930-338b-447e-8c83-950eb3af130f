"""
核心枚举定义
统一的枚举定义，避免重复和类型冲突
"""
import enum
from typing import Any
try:
    from pydantic_core import core_schema
    PYDANTIC_V2 = True
except ImportError:
    PYDANTIC_V2 = False


class TestType(str, enum.Enum):
    """测试类型枚举"""
    FUNCTIONAL = "FUNCTIONAL"
    PERFORMANCE = "PERFORMANCE"
    SECURITY = "SECURITY"
    COMPATIBILITY = "COMPATIBILITY"
    USABILITY = "USABILITY"
    INTERFACE = "INTERFACE"
    DATABASE = "DATABASE"


class TestLevel(str, enum.Enum):
    """测试级别枚举"""
    UNIT = "UNIT"
    INTEGRATION = "INTEGRATION"
    SYSTEM = "SYSTEM"
    ACCEPTANCE = "ACCEPTANCE"


class Priority(str, enum.Enum):
    """优先级枚举"""
    P0 = "P0"
    P1 = "P1"
    P2 = "P2"
    P3 = "P3"
    P4 = "P4"


class TestCaseStatus(str, enum.Enum):
    """测试用例状态枚举"""
    DRAFT = "DRAFT"
    APPROVED = "APPROVED"
    DEPRECATED = "DEPRECATED"


class InputSource(str, enum.Enum):
    """输入源类型枚举"""
    IMAGE = "IMAGE"
    DOCUMENT = "DOCUMENT"
    API_SPEC = "API_SPEC"
    DATABASE_SCHEMA = "DATABASE_SCHEMA"
    VIDEO = "VIDEO"
    MANUAL = "MANUAL"


class ProjectStatus(str, enum.Enum):
    """项目状态枚举"""
    ACTIVE = "ACTIVE"
    ARCHIVED = "ARCHIVED"

    @classmethod
    def normalize(cls, value: str) -> 'ProjectStatus':
        """标准化项目状态值，支持大小写转换"""
        if not value:
            return cls.ACTIVE

        # 尝试直接匹配
        try:
            return cls(value)
        except ValueError:
            pass

        # 尝试大写匹配
        try:
            return cls(value.upper())
        except ValueError:
            pass

        # 尝试小写匹配后转换
        value_lower = value.lower()
        if value_lower == "active":
            return cls.ACTIVE
        elif value_lower == "archived":
            return cls.ARCHIVED

        # 默认返回ACTIVE
        return cls.ACTIVE

    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: Any
    ):
        """Pydantic v2 核心模式定义"""
        if PYDANTIC_V2:
            return core_schema.no_info_before_validator_function(
                cls.normalize,
                core_schema.enum_schema(cls, list(cls))
            )
        else:
            # Fallback for older versions
            return handler(source_type)

    @classmethod
    def __get_validators__(cls):
        """Pydantic v1 兼容的验证器"""
        yield cls.validate

    @classmethod
    def validate(cls, value):
        """Pydantic 验证器，支持大小写转换"""
        if isinstance(value, cls):
            return value
        if isinstance(value, str):
            return cls.normalize(value)
        raise ValueError(f'Invalid ProjectStatus value: {value}')


class SessionType(str, enum.Enum):
    """会话类型枚举"""
    DOCUMENT_PARSE = "document_parse"
    IMAGE_ANALYSIS = "image_analysis"
    API_SPEC_PARSE = "api_spec_parse"
    DATABASE_SCHEMA_PARSE = "database_schema_parse"
    VIDEO_ANALYSIS = "video_analysis"
    MANUAL_INPUT = "manual_input"


class SessionStatus(str, enum.Enum):
    """会话状态枚举"""
    CREATED = "created"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class UploadSource(str, enum.Enum):
    """上传源类型枚举"""
    DOCUMENT = "DOCUMENT"
    IMAGE = "IMAGE"
    API_SPEC = "API_SPEC"
    DATABASE_SCHEMA = "DATABASE_SCHEMA"
    VIDEO = "VIDEO"


class ExportType(str, enum.Enum):
    """导出类型枚举"""
    EXCEL = "EXCEL"
    WORD = "WORD"
    PDF = "PDF"


class ExportStatus(str, enum.Enum):
    """导出状态枚举"""
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class ConfigType(str, enum.Enum):
    """配置类型枚举"""
    STRING = "STRING"
    NUMBER = "NUMBER"
    BOOLEAN = "BOOLEAN"
    JSON = "JSON"

2025-08-09 15:16:22 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:16:23 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:14 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:17 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:19 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_cases:285 | 获取测试用例列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:21 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:26 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:26 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:17:30 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:23:07 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:23:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:23:07 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:24 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:24 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:24:24 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:26 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:26 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:24:26 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:27:02 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:27:02 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:27:02 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:27:05 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:27:05 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:27:05 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:31:41 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:31:41 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:31:41 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:36:13 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:36:42 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:36:42 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:36:42 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:37:43 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:37:43 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:37:43 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:39:51 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:39:51 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:39:51 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:39:54 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:39:54 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:39:54 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:41:07 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:41:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:41:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:46:15 | WARNING  | app.api.v1.endpoints.projects:get_projects:156 | 项目 default-project-001 的状态 'active' 已标准化为 'ACTIVE'
2025-08-09 15:46:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:46:15 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:46:19 | WARNING  | app.api.v1.endpoints.projects:get_projects:156 | 项目 default-project-001 的状态 'active' 已标准化为 'ACTIVE'
2025-08-09 15:46:19 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:46:19 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:51:10 | INFO     | app.api.v1.endpoints.test_case_generator:create_generation_session:301 | 创建测试用例生成会话: 2090554b-4c3a-4e5f-b0c0-d649b00d715c, 类型: file
2025-08-09 15:51:11 | ERROR    | app.api.v1.endpoints.test_case_generator:upload_file:487 | 数据库状态更新失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:11 | ERROR    | app.api.v1.endpoints.test_case_generator:upload_file:499 | 数据库操作异常: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 数据库状态更新失败
2025-08-09 15:51:11 | INFO     | app.api.v1.endpoints.test_case_generator:upload_file:504 | 文件上传成功: 2090554b-4c3a-4e5f-b0c0-d649b00d715c, 文件: 鼎诚诚信一生终身寿险.pdf, 智能体: document_parser
2025-08-09 15:51:11 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:829 | 文件处理开始，会话 2090554b-4c3a-4e5f-b0c0-d649b00d715c 状态已更新为 processing
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始解析文档: 鼎诚诚信一生终身寿险.pdf
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📄 文档信息: 文件大小 103.3KB, 格式: .pdf
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 开始解析文档内容...
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📖 正在解析 .pdf 格式文档...
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📄 PDF文档信息: 共 1 页，开始文本提取...
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在提取第 1/1 页文本...
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 第 1 页文本提取成功 (耗时: 0.01秒, 内容: 3175 字符)
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 PDF文本提取统计: 成功 1 页, 失败 0 页, 总内容 3175 字符
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 正在整合页面内容...
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ PDF文本解析完成! 有效页面: 1, 内容整合耗时: 0.00秒
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📝 内容提取完成，耗时 0.09秒，内容长度: 3300 字符
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🤖 开始AI智能分析文档内容...
2025-08-09 15:51:14 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:14 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:14 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:14 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:14 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:14 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:14 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:14 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:14 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🧠 AI分析完成，耗时 20.74秒，置信度: 0.90
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 解析结果: 提取 1 个章节, 4 个需求
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 基于文档内容生成测试用例...
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 成功生成 6 个测试用例
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第3步: 保存需求信息到数据库...
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 4 个需求到数据库...
2025-08-09 15:51:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 文档解析完成! 处理时间: 20.84秒
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 转发到测试点提取智能体进行专业测试点分析...
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 4 个需求到数据库...
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 正在批量保存 4 个需求...
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 开始企业级测试点提取: 基于需求解析结果
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 需求分析输入: 6 个需求, 0 个业务流程
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 开始专业测试点提取分析...
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:35 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:35 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:35 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:35 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:35 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 需求存储智能体
2025-08-09 15:51:35 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:35 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:35 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 文档解析智能体
2025-08-09 15:51:35 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试点提取智能体
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 需求保存完成: 成功 4 个，失败 0 个
2025-08-09 15:51:35 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:36 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 需求保存成功: 4 个需求已保存到数据库
2025-08-09 15:51:36 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:36 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试点提取智能体
2025-08-09 15:51:36 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 需求存储智能体
2025-08-09 15:51:36 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 需求存储智能体
2025-08-09 15:51:36 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 需求存储智能体
2025-08-09 15:51:57 | INFO     | app.api.v1.endpoints.test_case_generator:create_generation_session:301 | 创建测试用例生成会话: 4cf4bbad-ae3b-4299-9d09-145dfff31f52, 类型: file
2025-08-09 15:51:57 | INFO     | app.api.v1.endpoints.test_case_generator:upload_file:504 | 文件上传成功: 4cf4bbad-ae3b-4299-9d09-145dfff31f52, 文件: 鼎诚诚信一生终身寿险.pdf, 智能体: document_parser
2025-08-09 15:51:58 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:829 | 文件处理开始，会话 4cf4bbad-ae3b-4299-9d09-145dfff31f52 状态已更新为 processing
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始解析文档: 鼎诚诚信一生终身寿险.pdf
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📄 文档信息: 文件大小 103.3KB, 格式: .pdf
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 开始解析文档内容...
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📖 正在解析 .pdf 格式文档...
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📄 PDF文档信息: 共 1 页，开始文本提取...
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在提取第 1/1 页文本...
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 第 1 页文本提取成功 (耗时: 0.01秒, 内容: 3175 字符)
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 PDF文本提取统计: 成功 1 页, 失败 0 页, 总内容 3175 字符
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 正在整合页面内容...
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ PDF文本解析完成! 有效页面: 1, 内容整合耗时: 0.00秒
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📝 内容提取完成，耗时 0.02秒，内容长度: 3300 字符
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🤖 开始AI智能分析文档内容...
2025-08-09 15:52:00 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🧠 AI分析完成，耗时 21.43秒，置信度: 0.85
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 解析结果: 提取 1 个章节, 4 个需求
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 基于文档内容生成测试用例...
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 成功生成 6 个测试用例
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第3步: 保存需求信息到数据库...
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 4 个需求到数据库...
2025-08-09 15:52:21 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 文档解析完成! 处理时间: 21.45秒
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 转发到测试点提取智能体进行专业测试点分析...
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 4 个需求到数据库...
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 正在批量保存 4 个需求...
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 开始企业级测试点提取: 基于需求解析结果
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 需求分析输入: 6 个需求, 0 个业务流程
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 开始专业测试点提取分析...
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 需求保存完成: 成功 4 个，失败 0 个
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 需求保存成功: 4 个需求已保存到数据库
2025-08-09 15:52:22 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📈 测试点提取完成: 功能测试点 2 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 8 个测试点
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 基于测试点生成测试用例...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 成功生成 6 个测试用例
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 测试点提取完成! 处理时间: 67.49秒
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 转发到测试用例生成智能体进行用例生成...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 开始企业级测试用例生成，基于专业测试点提取结果
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 测试点分析: 功能测试点 2 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 8 个测试点
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 基于测试点生成企业级测试用例...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 调用RAG知识库检索相关上下文...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始多维度RAG上下文检索...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ 未获取到RAG上下文信息
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 正在基于专业测试点和RAG上下文生成企业级测试用例...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📝 处理 2 个功能测试点...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始RAG知识库检索:  行业标准 测试规范 业务规则...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ R2R客户端未初始化，尝试重新连接...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始RAG知识库检索: 功能测试 非功能测试 集成测试 边界测试 异常测试 测试用例设计 测试方法 测试技术 等价类划分 边...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ R2R客户端未初始化，尝试重新连接...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始RAG知识库检索: 测试用例模板 测试场景 用例设计 测试步骤...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ R2R客户端未初始化，尝试重新连接...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始RAG知识库检索: 质量标准 测试覆盖度 验收标准 合规要求 性能标准 安全标准 可用性标准...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ R2R客户端未初始化，尝试重新连接...
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)
2025-08-09 15:52:43 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试点提取智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试点提取智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试点提取智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试点提取智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试点提取智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例生成智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例生成智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例生成智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例生成智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例生成智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - RAG知识库检索智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - RAG知识库检索智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - RAG知识库检索智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - RAG知识库检索智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - RAG知识库检索智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - RAG知识库检索智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - RAG知识库检索智能体
2025-08-09 15:52:43 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - RAG知识库检索智能体
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📈 测试点提取完成: 功能测试点 2 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 8 个测试点
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 基于测试点生成测试用例...
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 成功生成 6 个测试用例
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 测试点提取完成! 处理时间: 71.13秒
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 转发到测试用例生成智能体进行用例生成...
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 开始企业级测试用例生成，基于专业测试点提取结果
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 测试点分析: 功能测试点 2 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 8 个测试点
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第1步: 基于测试点生成企业级测试用例...
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 调用RAG知识库检索相关上下文...
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始多维度RAG上下文检索...
2025-08-09 15:53:33 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ 未获取到RAG上下文信息
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🏭 正在基于专业测试点和RAG上下文生成企业级测试用例...
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📝 处理 2 个功能测试点...
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始RAG知识库检索:  行业标准 测试规范 业务规则...
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ R2R客户端未初始化，尝试重新连接...
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始RAG知识库检索: 功能测试 非功能测试 集成测试 边界测试 异常测试 测试用例设计 测试方法 测试技术 等价类划分 边...
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ R2R客户端未初始化，尝试重新连接...
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始RAG知识库检索: 测试用例模板 测试场景 用例设计 测试步骤...
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ R2R客户端未初始化，尝试重新连接...
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 开始RAG知识库检索: 质量标准 测试覆盖度 验收标准 合规要求 性能标准 安全标准 可用性标准...
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ R2R客户端未初始化，尝试重新连接...
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)
2025-08-09 15:53:34 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:55:24 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚡ 处理 2 个非功能测试点...
2025-08-09 15:55:24 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:55:36 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚡ 处理 2 个非功能测试点...
2025-08-09 15:55:36 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:56:30 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 处理 1 个集成测试点...
2025-08-09 15:56:30 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:56:38 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔗 处理 1 个集成测试点...
2025-08-09 15:56:38 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:56:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 处理 1 个验收测试点...
2025-08-09 15:56:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:57:02 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 处理 1 个验收测试点...
2025-08-09 15:57:02 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:57:11 | WARNING  | app.api.v1.endpoints.projects:get_projects:156 | 项目 default-project-001 的状态 'active' 已标准化为 'ACTIVE'
2025-08-09 15:57:11 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 2
2025-08-09 15:57:11 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1136 | 内存会话 2090554b-4c3a-4e5f-b0c0-d649b00d715c: status=processing, progress=10.0
2025-08-09 15:57:11 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1136 | 内存会话 4cf4bbad-ae3b-4299-9d09-145dfff31f52: status=processing, progress=10.0
2025-08-09 15:57:11 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:57:19 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 处理 1 个边界测试点...
2025-08-09 15:57:19 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:57:32 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🎯 处理 1 个边界测试点...
2025-08-09 15:57:32 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:57:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🚨 处理 1 个异常测试点...
2025-08-09 15:57:41 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:57:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🚨 处理 1 个异常测试点...
2025-08-09 15:57:55 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 企业级测试用例生成完成: 总计 15 个测试用例, 质量评分: 0.80
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 企业级测试用例生成完成: 共生成 15 个测试用例
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 保存企业级测试用例到数据库...
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 15 个测试用例到数据库...
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在进行批量数据验证...
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📦 正在处理第 1/1 批数据 (15 个测试用例)...
2025-08-09 15:58:45 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📊 企业级测试用例生成完成: 总计 17 个测试用例, 质量评分: 0.80
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ✅ 企业级测试用例生成完成: 共生成 17 个测试用例
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔄 第2步: 保存企业级测试用例到数据库...
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 💾 开始保存 17 个测试用例到数据库...
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 🔍 正在进行批量数据验证...
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - 📦 正在处理第 1/1 批数据 (17 个测试用例)...
2025-08-09 15:58:54 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:59:04 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 4cf4bbad-ae3b-4299-9d09-145dfff31f52 - 测试用例生成智能体
2025-08-09 15:59:04 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 4cf4bbad-ae3b-4299-9d09-145dfff31f52 - 测试用例生成智能体
2025-08-09 15:59:04 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 4cf4bbad-ae3b-4299-9d09-145dfff31f52 - 测试用例生成智能体
2025-08-09 15:59:04 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ❌ 测试用例保存失败，成功 0 个，失败 1 个
2025-08-09 15:59:04 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:59:04 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ 企业级测试用例生成完成，但保存时出现问题：成功 0 个，失败 1 个
2025-08-09 15:59:04 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:59:04 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 4cf4bbad-ae3b-4299-9d09-145dfff31f52 - 测试用例保存智能体
2025-08-09 15:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:959 | 文件处理完成，从内存中移除会话: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:59:05 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:977 | 文件处理任务已启动: 4cf4bbad-ae3b-4299-9d09-145dfff31f52
2025-08-09 15:59:13 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例生成智能体
2025-08-09 15:59:13 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例生成智能体
2025-08-09 15:59:13 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例生成智能体
2025-08-09 15:59:13 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例保存智能体
2025-08-09 15:59:13 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ❌ 测试用例保存失败，成功 0 个，失败 1 个
2025-08-09 15:59:13 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:59:13 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:839 | 收到流式消息: message - ⚠️ 企业级测试用例生成完成，但保存时出现问题：成功 0 个，失败 1 个
2025-08-09 15:59:13 | INFO     | app.api.v1.endpoints.test_case_generator:message_callback:844 | 消息已放入队列: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:59:13 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例生成智能体
2025-08-09 15:59:13 | WARNING  | app.api.v1.endpoints.test_case_generator:_save_message_to_database:126 | 智能体消息保存失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 测试用例保存智能体
2025-08-09 15:59:14 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:959 | 文件处理完成，从内存中移除会话: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:59:14 | INFO     | app.api.v1.endpoints.test_case_generator:process_file:977 | 文件处理任务已启动: 2090554b-4c3a-4e5f-b0c0-d649b00d715c

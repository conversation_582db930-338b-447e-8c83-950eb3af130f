2025-08-09 15:16:22 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:16:23 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:14 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:17 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:19 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_cases:285 | 获取测试用例列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:21 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:26 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:30 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:23:07 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:23:07 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:24 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:24 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:26 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:26 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:27:02 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:27:05 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:31:41 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:36:13 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:36:42 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:37:43 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:39:51 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:39:54 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:41:07 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:51:10 | ERROR    | app.utils.session_db_utils:create_processing_session:53 | 创建处理会话记录失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c, 错误: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: 
                INSERT INTO processing_sessions (
                    id, session_type, status, progress, project_id,
                    input_data, config_data, agent_type, started_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, NOW()
                )
            ]
[parameters: ('2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'document_parse', 'created', 0.0, None, '{}', '{"analysis_target": "\\u751f\\u6210\\u6d4b\\u8bd5\\u7528\\u4f8b", "generate_mind_map": true, "export_excel": false, "max_test_cases": null, "tags": ["\\u529f\\u80fd\\u6d4b\\u8bd5", "\\u9700\\u6c42\\u6d4b\\u8bd5"], "input_type": "file"}', 'test_case_generator')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:51:11 | ERROR    | app.utils.session_db_utils:update_session_status:124 | 会话不存在: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:11 | ERROR    | app.api.v1.endpoints.test_case_generator:upload_file:487 | 数据库状态更新失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:11 | ERROR    | app.api.v1.endpoints.test_case_generator:upload_file:499 | 数据库操作异常: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - 数据库状态更新失败
2025-08-09 15:51:11 | ERROR    | app.utils.session_db_utils:update_session_status:124 | 会话不存在: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
2025-08-09 15:51:14 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('6d7bca14-f8bc-42d3-ab02-753e7a6faedd', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-120af702-c98d-4679-9a09-3355ceccbeec', '文档解析智能体', '文档解析智能体', 'info', '🔍 开始解析文档: 鼎诚诚信一生终身寿险.pdf', 'process', '文档解析智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 51, 13, 987712))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:14 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('991fbf92-6065-473f-8e2a-10fe35b012f3', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-33a80dad-257c-48be-9ccc-ad1b79013466', '文档解析智能体', '文档解析智能体', 'info', '🔄 第1步: 开始解析文档内容...', 'progress', '文档解析智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 51, 13, 988716))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:14 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('d1101579-ab85-4f61-9061-86df29a39dca', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-a3175d82-d9b5-41f8-b4cb-1ab0606f9015', '文档解析智能体', '文档解析智能体', 'info', '🤖 开始AI智能分析文档内容...', 'progress', '文档解析智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 51, 14, 76363))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:14 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('195e94b8-ef3d-48d2-83d3-86a0055adf76', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-16bcccaa-3986-49d3-95a8-98a5ba700f2e', '文档解析智能体', '文档解析智能体', 'success', '📊 PDF文本提取统计: 成功 1 页, 失败 0 页, 总内容 3175 字符', 'info', '文档解析智能体', False, '{"successful_pages": 1, "failed_pages": 0, "total_content_length": 3175, "success_rate": 100.0}', None, None, 'completion', datetime.datetime(2025, 8, 9, 15, 51, 14, 75359))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:14 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('2099a1ca-2705-4228-8426-bab5fd67d7e6', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-e8aa5553-6197-4fbb-8e1e-fd3b488443f4', '文档解析智能体', '文档解析智能体', 'success', '📝 内容提取完成，耗时 0.09秒，内容长度: 3300 字符', 'info', '文档解析智能体', False, '{"content_extraction_time": 0.086579, "content_length": 3300}', None, None, 'completion', datetime.datetime(2025, 8, 9, 15, 51, 14, 76363))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:14 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('2ad3bd2b-b044-429b-a2f9-31b59679d054', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-c25293f9-4bea-4510-8af3-ccd02c4a9d0d', '文档解析智能体', '文档解析智能体', 'success', '✅ PDF文本解析完成! 有效页面: 1, 内容整合耗时: 0.00秒', 'success', '文档解析智能体', False, '{"effective_pages": 1, "integration_time": 0.0, "final_content_length": 3300}', None, None, 'processing', datetime.datetime(2025, 8, 9, 15, 51, 14, 75359))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:14 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('9fe2f78a-1e79-48fe-9564-be22c205db52', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-26b418a4-ff3a-4463-9734-231bc13449d7', '文档解析智能体', '文档解析智能体', 'progress', '📖 正在解析 .pdf 格式文档...', 'progress', '文档解析智能体', False, None, None, None, 'processing', datetime.datetime(2025, 8, 9, 15, 51, 13, 988716))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:14 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('e17aba4b-5969-4a15-be1d-84fec269c623', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-3090230c-16cf-463b-95ff-877d04d4efa1', '文档解析智能体', '文档解析智能体', 'info', '📄 PDF文档信息: 共 1 页，开始文本提取...', 'info', '文档解析智能体', False, '{"total_pages": 1, "parsing_method": "text_extraction"}', None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 51, 14, 59620))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:14 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('b4a54c8f-38f4-480d-b88a-95ac038577eb', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-62cb9dab-472c-4f12-92e5-ae241a1c6b77', '文档解析智能体', '文档解析智能体', 'success', '✅ 第 1 页文本提取成功 (耗时: 0.01秒, 内容: 3175 字符)', 'success', '文档解析智能体', False, None, None, None, 'completion', datetime.datetime(2025, 8, 9, 15, 51, 14, 73852))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:35 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('240322b8-33c2-4117-a41d-a69f239b9564', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-a9634e18-224f-4e84-b031-5221e3065abf', '文档解析智能体', '文档解析智能体', 'success', '🧠 AI分析完成，耗时 20.74秒，置信度: 0.90', 'success', '文档解析智能体', False, '{"ai_analysis_time": 20.743834, "confidence_score": 0.9, "analysis_quality": "\\u9ad8"}', None, None, 'processing', datetime.datetime(2025, 8, 9, 15, 51, 34, 820197))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:35 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('1022b646-9d2c-4ae9-9ced-748c55450459', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-479f3d4b-0e8f-4fea-b710-b3da0e3a3157', '文档解析智能体', '文档解析智能体', 'metrics', '📊 解析结果: 提取 1 个章节, 4 个需求', 'info', '文档解析智能体', False, '{"sections_count": 1, "requirements_count": 4, "confidence_score": 0.9}', None, '{"sections_count": 1, "requirements_count": 4, "confidence_score": 0.9}', 'processing', datetime.datetime(2025, 8, 9, 15, 51, 34, 821197))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:35 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('f8b10b00-7a3c-4658-afa9-bf8c63282fb7', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-5be1c77b-811d-441a-9767-5686a46f6147', '文档解析智能体', '文档解析智能体', 'info', '💾 开始保存 4 个需求到数据库...', 'process', '文档解析智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 51, 34, 823198))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:35 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('356f498f-18ef-4e42-8e42-dbc87a4869f8', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-d73ad364-7bfd-4fa4-be09-180d2480b7f4', '文档解析智能体', '文档解析智能体', 'success', '✅ 成功生成 6 个测试用例', 'success', '文档解析智能体', False, '{"test_cases_count": 6}', None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 51, 34, 822196))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:35 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('b9166c80-4eff-472f-8586-3edbfb65cbb9', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-f1fd43be-fd7a-474b-b129-e72e4e01f786', '需求存储智能体', '需求存储智能体', 'info', '💾 开始保存 4 个需求到数据库...', 'process', '需求存储智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 51, 35, 177972))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:35 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('6ed1f387-29b7-4884-9097-30df59f2b065', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-3bddaf9b-faa8-4fbb-9a6b-525e701777d8', '文档解析智能体', '文档解析智能体', 'info', '🔄 第3步: 保存需求信息到数据库...', 'progress', '文档解析智能体', False, None, None, None, 'saving', datetime.datetime(2025, 8, 9, 15, 51, 34, 822196))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:35 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('9f763aed-c4aa-4097-ba3e-53566bee0904', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-44364e29-6825-4e75-ae3b-e35677b2745e', '文档解析智能体', '文档解析智能体', 'success', '✅ 文档解析完成! 处理时间: 20.84秒', 'success', '文档解析智能体', False, '{"processing_time": 20.835486, "total_test_cases": 6, "total_requirements": 4, "confidence_score": 0.9}', None, None, 'processing', datetime.datetime(2025, 8, 9, 15, 51, 34, 824198))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:35 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('eb868990-7c4d-43f9-aefe-aedb009ad345', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-99da832b-a035-453e-a3d5-c93222671987', '文档解析智能体', '文档解析智能体', 'info', '🔄 第2步: 基于文档内容生成测试用例...', 'progress', '文档解析智能体', False, None, None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 51, 34, 821197))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:35 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('ab2c3ecc-573f-4c14-a75d-fda082be554e', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-3a5e244f-f27a-4eeb-bc09-cadbc7a51efb', '测试点提取智能体', '测试点提取智能体', 'info', '🔄 第1步: 开始专业测试点提取分析...', 'progress', '测试点提取智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 51, 35, 522407))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:36 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('a2824eac-7e75-4043-aa9e-45e77ffdbe7e', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-d31e6f74-b5f8-4317-a11b-98bb8a5e5391', '测试点提取智能体', '测试点提取智能体', 'info', '🎯 开始企业级测试点提取: 基于需求解析结果', 'process', '测试点提取智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 51, 35, 520887))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:36 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('4d0c9643-07fe-4100-8b97-fed683fcf9a9', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-5b0b5975-f304-4e31-83dd-25c25eb96dd9', '需求存储智能体', '需求存储智能体', 'progress', '💾 正在批量保存 4 个需求...', 'process', '需求存储智能体', False, None, None, None, 'saving', datetime.datetime(2025, 8, 9, 15, 51, 35, 179154))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:36 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('d2c640fc-6486-4180-a3cb-ca23da8590a2', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-a6ca3632-a840-46f7-ae9a-01e6c3a85b6b', '需求存储智能体', '需求存储智能体', 'success', '✅ 需求保存完成: 成功 4 个，失败 0 个', 'process', '需求存储智能体', False, None, None, None, 'saving', datetime.datetime(2025, 8, 9, 15, 51, 35, 990726))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:51:36 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('d85e928d-7f11-40a6-807e-96c9a79ca96f', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-87d49632-b5a1-4fb2-9e2d-e2c20f7080f0', '需求存储智能体', '需求存储智能体', 'success', '✅ 需求保存成功: 4 个需求已保存到数据库', 'result', '需求存储智能体', False, None, None, None, 'saving', datetime.datetime(2025, 8, 9, 15, 51, 36, 47536))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.agents.test_case.rag_retrieval_agent:handle_rag_retrieval_request:166 | RAG知识库检索失败: 无法连接到R2R知识库服务
2025-08-09 15:52:43 | ERROR    | app.agents.test_case.rag_retrieval_agent:handle_rag_retrieval_request:166 | RAG知识库检索失败: 无法连接到R2R知识库服务
2025-08-09 15:52:43 | ERROR    | app.agents.test_case.rag_retrieval_agent:handle_rag_retrieval_request:166 | RAG知识库检索失败: 无法连接到R2R知识库服务
2025-08-09 15:52:43 | ERROR    | app.agents.test_case.rag_retrieval_agent:handle_rag_retrieval_request:166 | RAG知识库检索失败: 无法连接到R2R知识库服务
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('6ba51106-85a9-4c7e-aba7-4b514d526146', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-9314e548-687e-4aea-a808-504681f556fa', '测试点提取智能体', '测试点提取智能体', 'info', '🔄 第2步: 基于测试点生成测试用例...', 'progress', '测试点提取智能体', False, None, None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 52, 43, 6262))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('e47c0580-7f23-4496-a80c-809037be04ea', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-0c9fa7b9-6e31-41d5-bf1e-53a94de2f61e', '测试点提取智能体', '测试点提取智能体', 'success', '✅ 测试点提取完成! 处理时间: 67.49秒', 'success', '测试点提取智能体', False, '{"processing_time": 67.485895, "total_test_points": 8, "coverage_score": 0.95, "confidence_score": 0.94}', None, None, 'processing', datetime.datetime(2025, 8, 9, 15, 52, 43, 7797))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('dfc0f252-eb7b-497c-b348-51b7e471b7cf', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-0e7e9ad6-4096-4aab-af55-0305199759c4', '测试点提取智能体', '测试点提取智能体', 'success', '📈 测试点提取完成: 功能测试点 2 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 8 个测试点', 'info', '测试点提取智能体', False, '{"functional_test_points_count": 2, "non_functional_test_points_count": 2, "integration_test_points_count": 1, "acceptance_test_points_count": 1, "boundary_test_points_count": 1, "exception_test_points_count": 1, "total_test_points": 8, "confidence_score": 0.94}', None, None, 'completion', datetime.datetime(2025, 8, 9, 15, 52, 43, 5756))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('868f9fac-0479-4f07-be2a-d27baf7041a8', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-6ad8f897-d17f-4acd-a074-c287f90fda17', '测试点提取智能体', '测试点提取智能体', 'success', '✅ 成功生成 6 个测试用例', 'success', '测试点提取智能体', False, '{"test_cases_count": 6}', None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 52, 43, 6782))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('46952541-f023-498f-8b51-375676bae985', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-c47ece07-6309-463d-95a9-4d7c35473478', '测试点提取智能体', '测试点提取智能体', 'info', '🔄 转发到测试用例生成智能体进行用例生成...', 'info', '测试点提取智能体', False, None, None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 52, 43, 8310))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('7ab284e0-764e-46b2-8baa-3113ce1322b4', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-bcc1b05b-c4ec-4177-b396-aa70b961ac62', '测试用例生成智能体', '测试用例生成智能体', 'info', '🏭 开始企业级测试用例生成，基于专业测试点提取结果', 'process', '测试用例生成智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 52, 43, 243208))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('4f300a71-a9f4-48a3-b0a9-a53d0871cce7', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-1ec9d31c-9a80-4a96-8ec8-ff04bea71b95', '测试用例生成智能体', '测试用例生成智能体', 'metrics', '📊 测试点分析: 功能测试点 2 个, 非功能测试点 2 个, 集成测试点 1 个, 总计 8 个测试点', 'info', '测试用例生成智能体', False, '{"functional_test_points_count": 2, "non_functional_test_points_count": 2, "integration_test_points_count": 1, "acceptance_test_points_count": 1, "boundary_test_points_count": 1, "exception_test_points_count": 1, "total_test_points": 8}', None, '{"functional_test_points_count": 2, "non_functional_test_points_count": 2, "integration_test_points_count": 1, "acceptance_test_points_count": 1, "boundary_test_points_count": 1, "exception_test_points_count": 1, "total_test_points": 8}', 'processing', datetime.datetime(2025, 8, 9, 15, 52, 43, 244208))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('a91be6e5-e5b9-4b22-b8f1-65b7d8b0dccc', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-46bbb093-37f8-4aa9-8aac-4dd15637182e', '测试用例生成智能体', '测试用例生成智能体', 'info', '🔄 第1步: 基于测试点生成企业级测试用例...', 'progress', '测试用例生成智能体', False, None, None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 52, 43, 244208))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('c460d179-b9b8-4822-a679-394d98b89753', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-9138864f-c1f1-4fc6-a67c-0fa08b50c6d8', '测试用例生成智能体', '测试用例生成智能体', 'info', '🔍 开始多维度RAG上下文检索...', 'progress', '测试用例生成智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 52, 43, 245209))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('c7743eb9-1f79-4ff2-9fb3-e6d67a527a4a', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-f27c8701-70ff-4fef-aa76-0fdc2c84201c', '测试用例生成智能体', '测试用例生成智能体', 'progress', '🏭 正在基于专业测试点和RAG上下文生成企业级测试用例...', 'progress', '测试用例生成智能体', False, None, None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 52, 43, 248470))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('9a7d7e70-1139-4d9c-ae8c-87e8a8ea1e77', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-d97b5c00-5173-4cc0-b234-a72e6b09395e', 'RAG知识库检索智能体', 'RAG知识库检索智能体', 'info', '🔍 开始RAG知识库检索:  行业标准 测试规范 业务规则...', 'process', 'RAG知识库检索智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 52, 43, 689997))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('8872d4c5-906d-4e03-b749-58644ea2645c', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-4f857427-85c3-49f4-ade9-dd77d364acfa', 'RAG知识库检索智能体', 'RAG知识库检索智能体', 'completion', '❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)', 'error', 'RAG知识库检索智能体', True, '{"processing_time": 0.001509, "error": "\\u65e0\\u6cd5\\u8fde\\u63a5\\u5230R2R\\u77e5\\u8bc6\\u5e93\\u670d\\u52a1"}', None, None, 'processing', datetime.datetime(2025, 8, 9, 15, 52, 43, 692513))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('b700a71e-9557-4fe9-b0f5-03634f6c7295', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-ba7d0d0e-04d7-42f7-8479-9f412e876795', 'RAG知识库检索智能体', 'RAG知识库检索智能体', 'completion', '❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)', 'error', 'RAG知识库检索智能体', True, '{"processing_time": 0.002002, "error": "\\u65e0\\u6cd5\\u8fde\\u63a5\\u5230R2R\\u77e5\\u8bc6\\u5e93\\u670d\\u52a1"}', None, None, 'processing', datetime.datetime(2025, 8, 9, 15, 52, 43, 700597))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('a31c6d77-63ed-4955-b00f-b438bd3cde92', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-74aee84d-ba62-4f65-8698-d9c29e79577f', 'RAG知识库检索智能体', 'RAG知识库检索智能体', 'completion', '❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)', 'error', 'RAG知识库检索智能体', True, '{"processing_time": 0.001512, "error": "\\u65e0\\u6cd5\\u8fde\\u63a5\\u5230R2R\\u77e5\\u8bc6\\u5e93\\u670d\\u52a1"}', None, None, 'processing', datetime.datetime(2025, 8, 9, 15, 52, 43, 696530))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('96a56e29-feea-40ac-bb77-8865836db772', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-b845e6ba-a52a-48c6-ad32-9e7189763dee', 'RAG知识库检索智能体', 'RAG知识库检索智能体', 'info', '🔍 开始RAG知识库检索: 测试用例模板 测试场景 用例设计 测试步骤...', 'process', 'RAG知识库检索智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 52, 43, 698595))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('b577645b-8584-4f74-b640-6b1176896f10', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-b34fa3d4-95ea-4d6b-a363-2c836bd4958a', 'RAG知识库检索智能体', 'RAG知识库检索智能体', 'info', '🔍 开始RAG知识库检索: 功能测试 非功能测试 集成测试 边界测试 异常测试 测试用例设计 测试方法 测试技术 等价类划分 边...', 'process', 'RAG知识库检索智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 52, 43, 694018))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('a737a93d-e7ce-46d4-9a1f-8964d4131287', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-93400aae-829f-4a32-bbf2-a234903f4e22', 'RAG知识库检索智能体', 'RAG知识库检索智能体', 'info', '🔍 开始RAG知识库检索: 质量标准 测试覆盖度 验收标准 合规要求 性能标准 安全标准 可用性标准...', 'process', 'RAG知识库检索智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 52, 43, 701596))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:52:43 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('56b8de0f-cf21-4c89-8a8e-6916cb8e8cb6', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-68b7fbc7-980b-4176-8638-3183e973892a', 'RAG知识库检索智能体', 'RAG知识库检索智能体', 'completion', '❌ RAG知识库检索失败: 无法连接到R2R知识库服务 (处理时间: 0.00秒)', 'error', 'RAG知识库检索智能体', True, '{"processing_time": 0.001999, "error": "\\u65e0\\u6cd5\\u8fde\\u63a5\\u5230R2R\\u77e5\\u8bc6\\u5e93\\u670d\\u52a1"}', None, None, 'processing', datetime.datetime(2025, 8, 9, 15, 52, 43, 703595))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:53:34 | ERROR    | app.agents.test_case.rag_retrieval_agent:handle_rag_retrieval_request:166 | RAG知识库检索失败: 无法连接到R2R知识库服务
2025-08-09 15:53:34 | ERROR    | app.agents.test_case.rag_retrieval_agent:handle_rag_retrieval_request:166 | RAG知识库检索失败: 无法连接到R2R知识库服务
2025-08-09 15:53:34 | ERROR    | app.agents.test_case.rag_retrieval_agent:handle_rag_retrieval_request:166 | RAG知识库检索失败: 无法连接到R2R知识库服务
2025-08-09 15:53:34 | ERROR    | app.agents.test_case.rag_retrieval_agent:handle_rag_retrieval_request:166 | RAG知识库检索失败: 无法连接到R2R知识库服务
2025-08-09 15:58:45 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_test_coverage:1101 | 分析测试覆盖度失败: 'NoneType' object has no attribute 'lower'
2025-08-09 15:58:54 | ERROR    | app.agents.test_case.test_case_generator_agent:_analyze_test_coverage:1101 | 分析测试覆盖度失败: 'NoneType' object has no attribute 'lower'
2025-08-09 15:59:04 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 4cf4bbad-ae3b-4299-9d09-145dfff31f52 - (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('4cbb7192-2fe2-435c-8d77-5d1acad3a7ae', '4cf4bbad-ae3b-4299-9d09-145dfff31f52', 'orchestrator-66d1f2e1-112a-4622-8792-f4006039337c', '测试用例生成智能体', '测试用例生成智能体', 'success', '📊 企业级测试用例生成完成: 总计 15 个测试用例, 质量评分: 0.80', 'success', '测试用例生成智能体', False, '{"total_count": 15, "categories": {"functional": 6, "non_functional": 3, "integration": 1, "acceptance": 1, "boundary": 1, "exception": 3}, "quality_score": 0.8, "automation_score": 0.84, "coverage_score": 0.5}', None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 58, 45, 99400))]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:59:04 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 4cf4bbad-ae3b-4299-9d09-145dfff31f52 - (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('286dc109-179c-4c4f-b058-e4c01d9a11ad', '4cf4bbad-ae3b-4299-9d09-145dfff31f52', 'orchestrator-c1e77834-b38f-4c9e-98c0-d4bc1c7e6398', '测试用例生成智能体', '测试用例生成智能体', 'success', '✅ 企业级测试用例生成完成: 共生成 15 个测试用例', 'success', '测试用例生成智能体', False, '{"generated_count": 15, "generation_time": 311.456597, "test_case_categories": {"functional": 6, "non_functional": 3, "integration": 1, "acceptance": 1, "boundary": 1, "exception": 3}, "quality_score": 0.8}', None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 58, 45, 99400))]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:59:04 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 4cf4bbad-ae3b-4299-9d09-145dfff31f52 - (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('b475e40a-b7de-4337-82dd-440b2b5fcf43', '4cf4bbad-ae3b-4299-9d09-145dfff31f52', 'orchestrator-7118866d-d81a-4c06-ae0a-dc271169a00a', '测试用例生成智能体', '测试用例生成智能体', 'info', '🔄 第2步: 保存企业级测试用例到数据库...', 'progress', '测试用例生成智能体', False, None, None, None, 'saving', datetime.datetime(2025, 8, 9, 15, 58, 45, 99400))]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:59:04 | ERROR    | app.database.repositories.test_case_repository:ensure_system_data:56 | 确保系统数据失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT projects.id, projects.name, projects.description, projects.status, projects.created_at, projects.updated_at 
FROM projects 
WHERE projects.id = %s]
[parameters: ('default-project-001',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:59:04 | ERROR    | app.database.repositories.test_case_repository:create_test_case:111 | 创建测试用例失败: Can't reconnect until invalid transaction is rolled back.  Please rollback() fully before proceeding (Background on this error at: https://sqlalche.me/e/20/8s2b)
2025-08-09 15:59:04 | ERROR    | app.agents.database.test_case_saver_agent:_save_test_case_batch:420 | 保存第 1 个测试用例失败: Can't reconnect until invalid transaction is rolled back.  Please rollback() fully before proceeding (Background on this error at: https://sqlalche.me/e/20/8s2b)
2025-08-09 15:59:04 | ERROR    | app.agents.database.test_case_saver_agent:_log_save_error:1173 | 测试用例保存错误详情: {
  "index": 1,
  "title": "基本保费计算功能测试 - 10年交费期间30岁投保",
  "test_type": "FUNCTIONAL",
  "error_type": "PendingRollbackError",
  "error_message": "Can't reconnect until invalid transaction is rolled back.  Please rollback() fully before proceeding (Background on this error at: https://sqlalche.me/e/20/8s2b)",
  "timestamp": "2025-08-09T15:59:04.510793"
}
2025-08-09 15:59:04 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 4cf4bbad-ae3b-4299-9d09-145dfff31f52 - (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('78291e3e-eb65-4a30-ae72-58c5ba9ed458', '4cf4bbad-ae3b-4299-9d09-145dfff31f52', 'orchestrator-89e31322-1223-40be-af5f-b406aeb5a9d6', '测试用例保存智能体', '测试用例保存智能体', 'info', '💾 开始保存 15 个测试用例到数据库...', 'process', '测试用例保存智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 58, 45, 327661))]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:59:13 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('2cbf987c-d374-4b7d-b903-b0cb8fe1eb6e', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-9ddcd054-f5b7-4e25-91fe-1cd6460a794b', '测试用例生成智能体', '测试用例生成智能体', 'success', '✅ 企业级测试用例生成完成: 共生成 17 个测试用例', 'success', '测试用例生成智能体', False, '{"generated_count": 17, "generation_time": 370.962553, "test_case_categories": {"functional": 8, "non_functional": 3, "integration": 1, "acceptance": 1, "boundary": 1, "exception": 3}, "quality_score": 0.8}', None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 58, 54, 207768))]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:59:13 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('e14ea0fb-4a69-48d2-b724-9ce4e05eceeb', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-de30621b-2d99-4a39-aef9-148c3270d71e', '测试用例生成智能体', '测试用例生成智能体', 'success', '📊 企业级测试用例生成完成: 总计 17 个测试用例, 质量评分: 0.80', 'success', '测试用例生成智能体', False, '{"total_count": 17, "categories": {"functional": 8, "non_functional": 3, "integration": 1, "acceptance": 1, "boundary": 1, "exception": 3}, "quality_score": 0.8, "automation_score": 0.865, "coverage_score": 0.5}', None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 58, 54, 206761))]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:59:13 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('3bc9a4d6-75ee-4d77-8290-49128d99d10b', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-6f5330a9-8ec6-40a9-8936-fc78808f7366', '测试用例生成智能体', '测试用例生成智能体', 'info', '🔄 第2步: 保存企业级测试用例到数据库...', 'progress', '测试用例生成智能体', False, None, None, None, 'saving', datetime.datetime(2025, 8, 9, 15, 58, 54, 207768))]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:59:13 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('80e20350-2613-41a4-801f-457e858de62b', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-24639cb3-f8e1-4be4-9b07-c51f19edf78c', '测试用例保存智能体', '测试用例保存智能体', 'info', '💾 开始保存 17 个测试用例到数据库...', 'process', '测试用例保存智能体', False, None, None, None, 'initialization', datetime.datetime(2025, 8, 9, 15, 58, 54, 475083))]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:59:13 | ERROR    | app.database.repositories.test_case_repository:ensure_system_data:56 | 确保系统数据失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT projects.id, projects.name, projects.description, projects.status, projects.created_at, projects.updated_at 
FROM projects 
WHERE projects.id = %s]
[parameters: ('default-project-001',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:59:13 | ERROR    | app.database.repositories.test_case_repository:create_test_case:111 | 创建测试用例失败: Can't reconnect until invalid transaction is rolled back.  Please rollback() fully before proceeding (Background on this error at: https://sqlalche.me/e/20/8s2b)
2025-08-09 15:59:13 | ERROR    | app.agents.database.test_case_saver_agent:_save_test_case_batch:420 | 保存第 1 个测试用例失败: Can't reconnect until invalid transaction is rolled back.  Please rollback() fully before proceeding (Background on this error at: https://sqlalche.me/e/20/8s2b)
2025-08-09 15:59:13 | ERROR    | app.agents.database.test_case_saver_agent:_log_save_error:1173 | 测试用例保存错误详情: {
  "index": 1,
  "title": "保险费率计算功能测试 - 标准投保年龄费率计算",
  "test_type": "FUNCTIONAL",
  "error_type": "PendingRollbackError",
  "error_message": "Can't reconnect until invalid transaction is rolled back.  Please rollback() fully before proceeding (Background on this error at: https://sqlalche.me/e/20/8s2b)",
  "timestamp": "2025-08-09T15:59:13.663684"
}
2025-08-09 15:59:13 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('d48d2d27-c4b2-4d37-b32e-7d74fb95499f', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-9352a560-65f9-4f8c-b3fd-c9b2360caefc', '测试用例生成智能体', '测试用例生成智能体', 'completion', '⚠️ 企业级测试用例生成完成，但保存时出现问题：成功 0 个，失败 1 个', 'process', '测试用例生成智能体', True, '{"session_id": "2090554b-4c3a-4e5f-b0c0-d649b00d715c", "timestamp": "2025-08-09T15:59:13.667705", "generation_id": "b430b1da-d80d-4e5d-9f5b-3307846ec ... (44 characters truncated) ... , "generated_count": 17, "test_case_ids": [], "mind_map_generated": false, "processing_time": 390.424497, "created_at": "2025-08-09T15:59:13.667705"}', None, None, 'generation', datetime.datetime(2025, 8, 9, 15, 59, 13, 667705))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:59:13 | ERROR    | app.utils.agent_message_log_utils:save_agent_message_log:79 | 保存智能体消息日志失败: 2090554b-4c3a-4e5f-b0c0-d649b00d715c - (pymysql.err.IntegrityError) (1452, 'Cannot add or update a child row: a foreign key constraint fails (`case_demo`.`agent_message_logs`, CONSTRAINT `agent_message_logs_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `processing_sessions` (`id`) ON DELETE CASCADE)')
[SQL: 
                INSERT INTO agent_message_logs (
                    id, session_id, message_id, agent_type, agent_name,
                    message_type, content, region, source, is_final,
                    result_data, error_info, metrics_data, processing_stage,
                    timestamp, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, NOW()
                )
            ]
[parameters: ('740422fc-a5ba-4ab3-a49c-a318d6a4f2f5', '2090554b-4c3a-4e5f-b0c0-d649b00d715c', 'orchestrator-6d07fdd2-0d9b-4a76-b248-9f426d18df41', '测试用例保存智能体', '测试用例保存智能体', 'completion', '❌ 测试用例保存失败，成功 0 个，失败 1 个', 'process', '测试用例保存智能体', True, '{"session_id": "2090554b-4c3a-4e5f-b0c0-d649b00d715c", "success": false, "saved_count": 0, "failed_count": 1, "saved_test_cases": [], "errors": ["\\u ... (118 characters truncated) ... rolled back.  Please rollback() fully before proceeding (Background on this error at: https://sqlalche.me/e/20/8s2b)"], "processing_time": 19.189607}', None, None, 'saving', datetime.datetime(2025, 8, 9, 15, 59, 13, 664690))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-09 15:59:14 | ERROR    | app.utils.session_db_utils:update_session_status:124 | 会话不存在: 2090554b-4c3a-4e5f-b0c0-d649b00d715c
